from Cryptodome.Util.number import long_to_bytes
import math

# Given values
hint1 = ***************************************
hint2 = 130654136341446094800376989317102537325
n = 1291778230841963634710522186531131140292748304311790700929719174642140386189828346122801056721461179519840234314280632436994655344881023892312594913853574461748121277453328656446109784054563731
e = 9397905637403387422411461938505089525132522490010480161341814566119369497062528168320590767152928258571447916140517
c = 482782367816881259357312883356702175242817718119063880833819462767226937212873552015335218158868462980872863563953024168114906381978834311555560455076311389674805493391941801398577027462103318

print("=== Direct Attack on Broken RSA ===")

# The key insight: gcd(n, e) = y > 1, which breaks RSA
y = math.gcd(n, e)
print(f"gcd(n, e) = y = {y}")

# We have:
# n = p * q * y
# e = x * y * z
# This means n and e share the common factor y

# Let's try a different approach: since gcd(n, e) > 1, 
# the RSA encryption might be vulnerable to a direct attack

# Method 1: Try to use the fact that e and n are not coprime
print("\n[Method 1] Exploiting non-coprime e and n...")

# Since gcd(n, e) = y, we can write:
# n = y * n'  where n' = p * q
# e = y * e'  where e' = x * z

n_prime = n // y
e_prime = e // y

print(f"n' = n/y = {n_prime}")
print(f"e' = e/y = {e_prime}")

# Now, the original encryption was: c ≡ m^e (mod n)
# This becomes: c ≡ m^(y*e') (mod y*n')

# Method 2: Try to exploit the structure directly
print("\n[Method 2] Trying to decrypt without full factorization...")

# Since n = p*q*y and e = x*y*z, let's see if we can work with this
# The Euler's totient function is φ(n) = φ(p*q*y) = φ(p)*φ(q)*φ(y) = (p-1)*(q-1)*(y-1)

# But we don't know p and q individually. However, we know p*q = n/y
pq = n // y
print(f"p*q = {pq}")

# Let's try a different approach: maybe we can use the Chinese Remainder Theorem
# or exploit the fact that the encryption is done modulo n = p*q*y

# Method 3: Try to use the hints more directly
print("\n[Method 3] Using hints to constrain the problem...")

# We know:
# hint1 = p % x
# hint2 = p % z
# And we need to find x and z such that x*z = e/y

xz = e // y
print(f"x*z = {xz}")

# Since this is a CTF, maybe the factors have special properties
# Let's check if x*z has any obvious structure

print(f"x*z in binary: {bin(xz)}")
print(f"x*z in hex: {hex(xz)}")

# Method 4: Try to use the mathematical relationship more cleverly
print("\n[Method 4] Trying mathematical shortcuts...")

# Since we have p ≡ hint1 (mod x) and p ≡ hint2 (mod z),
# and we know p*q = n/y, maybe we can set up equations

# Let's assume x and z are close to each other (common in CTFs)
# If x ≈ z ≈ sqrt(x*z), then we can try values around sqrt(x*z)

import math
sqrt_xz = int(math.sqrt(xz))
print(f"sqrt(x*z) ≈ {sqrt_xz}")

# Try values around the square root
def extended_gcd(a, b):
    if a == 0:
        return b, 0, 1
    gcd, x1, y1 = extended_gcd(b % a, a)
    x = y1 - (b // a) * x1
    y = x1
    return gcd, x, y

def crt_two(a1, m1, a2, m2):
    """Chinese Remainder Theorem for two congruences"""
    gcd, u, v = extended_gcd(m1, m2)
    if (a2 - a1) % gcd != 0:
        return None
    lcm = m1 * m2 // gcd
    solution = (a1 + m1 * ((a2 - a1) // gcd) * u) % lcm
    return solution

# Try different factor pairs around the square root
print("Searching for factor pairs...")
for delta in range(1, 10000):
    for x_candidate in [sqrt_xz - delta, sqrt_xz + delta]:
        if x_candidate > 0 and xz % x_candidate == 0:
            z_candidate = xz // x_candidate
            
            if x_candidate != z_candidate:  # Make sure they're different
                print(f"Found potential factors: x={x_candidate}, z={z_candidate}")
                
                # Try both assignments
                for x, z in [(x_candidate, z_candidate), (z_candidate, x_candidate)]:
                    # Use CRT to find p
                    p_base = crt_two(hint1, x, hint2, z)
                    if p_base is None:
                        continue
                    
                    # Try different values of p
                    mod_xz = x * z
                    for k in range(-100, 101):
                        p_test = p_base + k * mod_xz
                        if p_test > 0 and pq % p_test == 0:
                            q_test = pq // p_test
                            
                            # Verify
                            if p_test * q_test * y == n:
                                print(f"✓ Found p = {p_test}")
                                print(f"✓ Found q = {q_test}")
                                
                                # Decrypt
                                phi_n = (p_test - 1) * (q_test - 1) * (y - 1)
                                
                                try:
                                    d = pow(e, -1, phi_n)
                                    m = pow(c, d, n)
                                    flag = long_to_bytes(m)
                                    print(f"\n🎉 FLAG: {flag.decode()}")
                                    exit()
                                except Exception as ex:
                                    print(f"Decryption error: {ex}")
                                    continue

print("Direct methods failed. The factors might be more complex.")

# Method 5: Try a probabilistic approach
print("\n[Method 5] Trying probabilistic factorization...")

def pollard_rho_optimized(n, max_iterations=1000000):
    """Optimized Pollard's rho with different starting points"""
    import random
    
    for seed in range(2, 20):  # Try different seeds
        x = seed
        y = seed
        d = 1
        
        f = lambda x: (x * x + 1) % n
        
        for _ in range(max_iterations):
            x = f(x)
            y = f(f(y))
            d = math.gcd(abs(x - y), n)
            
            if 1 < d < n:
                return d
            if d == n:
                break  # Try next seed
    
    return None

print("Trying optimized Pollard's rho...")
factor = pollard_rho_optimized(xz)
if factor:
    print(f"Pollard's rho found factor: {factor}")
    other_factor = xz // factor
    print(f"Other factor: {other_factor}")
    
    # Continue with the solution...
    # (Same logic as above)
else:
    print("Pollard's rho failed")

print("\nIf all methods fail, this might require specialized factorization tools or the number might have been pre-factored in a database.")
