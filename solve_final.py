from Cryptodome.Util.number import long_to_bytes
import math

# Given values
hint1 = ***************************************
hint2 = 130654136341446094800376989317102537325
n = 1291778230841963634710522186531131140292748304311790700929719174642140386189828346122801056721461179519840234314280632436994655344881023892312594913853574461748121277453328656446109784054563731
e = 9397905637403387422411461938505089525132522490010480161341814566119369497062528168320590767152928258571447916140517
c = 482782367816881259357312883356702175242817718119063880833819462767226937212873552015335218158868462980872863563953024168114906381978834311555560455076311389674805493391941801398577027462103318

print("=== Final Solution with Known Factors ===")

# Step 1: Get y from gcd
y = math.gcd(n, e)
print(f"y = {y}")

# Step 2: Get p*q and x*z
pq = n // y
xz = e // y
print(f"p*q = {pq}")
print(f"x*z = {xz}")

# Step 3: Use the known factorization of x*z
x_candidate = 205985756524450894105569840071389752521
z_candidate = 212007435030018912792096086712981924541

print(f"\nUsing known factors:")
print(f"x = {x_candidate}")
print(f"z = {z_candidate}")
print(f"Verification: x * z = {x_candidate * z_candidate}")
print(f"Expected x * z = {xz}")
print(f"Match: {x_candidate * z_candidate == xz}")

def extended_gcd(a, b):
    if a == 0:
        return b, 0, 1
    gcd, x1, y1 = extended_gcd(b % a, a)
    x = y1 - (b // a) * x1
    y = x1
    return gcd, x, y

def crt_two(a1, m1, a2, m2):
    """Chinese Remainder Theorem for two congruences"""
    gcd, u, v = extended_gcd(m1, m2)
    if (a2 - a1) % gcd != 0:
        return None
    lcm = m1 * m2 // gcd
    solution = (a1 + m1 * ((a2 - a1) // gcd) * u) % lcm
    return solution

# Try both assignments of x and z
for x, z in [(x_candidate, z_candidate), (z_candidate, x_candidate)]:
    print(f"\n--- Trying x={x}, z={z} ---")
    
    # Use CRT to find p: p ≡ hint1 (mod x), p ≡ hint2 (mod z)
    p_base = crt_two(hint1, x, hint2, z)
    if p_base is None:
        print("CRT failed for this assignment")
        continue
    
    print(f"CRT solution: p ≡ {p_base} (mod {x*z})")
    
    # Find the correct p by trying p = p_base + k*(x*z)
    mod_xz = x * z
    found_solution = False
    
    for k in range(-1000, 1001):
        p_test = p_base + k * mod_xz
        if p_test <= 0:
            continue
            
        if pq % p_test == 0:
            q_test = pq // p_test
            
            # Verify the solution
            if p_test * q_test * y == n:
                print(f"✓ Found valid p = {p_test}")
                print(f"✓ Found valid q = {q_test}")
                print(f"✓ Found valid x = {x}")
                print(f"✓ Found valid z = {z}")
                print(f"✓ Found valid y = {y}")
                
                # Verify all relationships
                print(f"\nVerification:")
                print(f"p * q * y = {p_test * q_test * y}")
                print(f"Original n = {n}")
                print(f"n match: {p_test * q_test * y == n}")
                print(f"x * y * z = {x * y * z}")
                print(f"Original e = {e}")
                print(f"e match: {x * y * z == e}")
                print(f"p % x = {p_test % x} (should be {hint1})")
                print(f"hint1 match: {p_test % x == hint1}")
                print(f"p % z = {p_test % z} (should be {hint2})")
                print(f"hint2 match: {p_test % z == hint2}")
                
                # Decrypt the message
                phi_n = (p_test - 1) * (q_test - 1) * (y - 1)
                print(f"\nφ(n) = (p-1)(q-1)(y-1) = {phi_n}")
                
                gcd_e_phi = math.gcd(e, phi_n)
                print(f"gcd(e, φ(n)) = {gcd_e_phi}")
                
                if gcd_e_phi == 1:
                    try:
                        d = pow(e, -1, phi_n)
                        print(f"Private exponent d = {d}")
                        
                        m = pow(c, d, n)
                        print(f"Decrypted message (as number): {m}")
                        
                        flag = long_to_bytes(m)
                        print(f"\n🎉 FLAG: {flag.decode()}")
                        found_solution = True
                        break
                    except Exception as ex:
                        print(f"Decryption error: {ex}")
                        continue
                else:
                    print(f"gcd(e, φ(n)) = {gcd_e_phi} ≠ 1")
                    print("This shouldn't happen with proper RSA parameters")
                    continue
    
    if found_solution:
        break

if not found_solution:
    print("Could not find a valid solution")
