from Cryptodome.Util.number import long_to_bytes
import math

# Given values
hint1 = ***************************************
hint2 = 130654136341446094800376989317102537325
n = 1291778230841963634710522186531131140292748304311790700929719174642140386189828346122801056721461179519840234314280632436994655344881023892312594913853574461748121277453328656446109784054563731
e = 9397905637403387422411461938505089525132522490010480161341814566119369497062528168320590767152928258571447916140517
c = 482782367816881259357312883356702175242817718119063880833819462767226937212873552015335218158868462980872863563953024168114906381978834311555560455076311389674805493391941801398577027462103318

print("=== Alternative Approach ===")

# Step 1: Get y from gcd
y = math.gcd(n, e)
print(f"y = {y}")

# Step 2: Get p*q and x*z
pq = n // y
xz = e // y
print(f"p*q = {pq}")
print(f"x*z = {xz}")

# Step 3: Try a different approach - maybe the numbers are specially constructed
# Let's check if x*z has any obvious patterns or if we can use the hints more directly

print(f"\nAnalyzing x*z = {xz}")
print(f"x*z in hex: {hex(xz)}")
print(f"x*z bit length: {xz.bit_length()}")

# Since x and z are 128-bit primes, let's try to see if there are patterns
# or if we can use a more targeted approach

# Let's try to use the Chinese Remainder Theorem approach but with a twist
# We know that p ≡ hint1 (mod x) and p ≡ hint2 (mod z)
# If we can guess or brute force x and z, we can solve for p

def extended_gcd(a, b):
    if a == 0:
        return b, 0, 1
    gcd, x1, y1 = extended_gcd(b % a, a)
    x = y1 - (b // a) * x1
    y = x1
    return gcd, x, y

def crt_two(a1, m1, a2, m2):
    """Chinese Remainder Theorem for two congruences"""
    gcd, u, v = extended_gcd(m1, m2)
    if (a2 - a1) % gcd != 0:
        return None
    lcm = m1 * m2 // gcd
    solution = (a1 + m1 * ((a2 - a1) // gcd) * u) % lcm
    return solution

# Let's try a different factorization approach
# Maybe use the fact that this is a CTF and the numbers might have special properties

def try_factorize_xz():
    """Try different methods to factorize x*z"""
    
    # Method 1: Check if it's a perfect square or has small factors
    sqrt_xz = int(xz ** 0.5)
    if sqrt_xz * sqrt_xz == xz:
        print(f"x*z is a perfect square! sqrt = {sqrt_xz}")
        return sqrt_xz, sqrt_xz
    
    # Method 2: Try Fermat's factorization (good for numbers close to each other)
    print("Trying Fermat's factorization...")
    a = int(xz ** 0.5) + 1
    for i in range(10000):
        b_squared = a * a - xz
        if b_squared >= 0:
            b = int(b_squared ** 0.5)
            if b * b == b_squared:
                factor1 = a + b
                factor2 = a - b
                if factor1 * factor2 == xz and factor1 > 1 and factor2 > 1:
                    print(f"Fermat's method found: {factor1} * {factor2}")
                    return factor1, factor2
        a += 1
    
    # Method 3: Try some heuristics based on the bit patterns
    print("Trying pattern-based approaches...")
    
    # Check if the factors might be close to powers of 2
    for exp in range(120, 135):
        base = 2 ** exp
        for offset in range(-1000, 1001):
            candidate = base + offset
            if xz % candidate == 0:
                other = xz // candidate
                print(f"Found factor near 2^{exp}: {candidate} * {other}")
                return candidate, other
    
    return None, None

# Try to factorize x*z
x_candidate, z_candidate = try_factorize_xz()

if x_candidate and z_candidate:
    print(f"\nFound potential factors: x={x_candidate}, z={z_candidate}")
    
    # Try both assignments
    for x, z in [(x_candidate, z_candidate), (z_candidate, x_candidate)]:
        print(f"\nTrying x={x}, z={z}")
        
        # Use CRT to find p
        p_base = crt_two(hint1, x, hint2, z)
        if p_base is None:
            print("CRT failed")
            continue
        
        print(f"CRT gives p ≡ {p_base} (mod {x*z})")
        
        # Try different values of p
        mod_xz = x * z
        for k in range(-100, 101):
            p_test = p_base + k * mod_xz
            if p_test > 0 and pq % p_test == 0:
                q_test = pq // p_test
                
                # Verify
                if p_test * q_test * y == n:
                    print(f"✓ Found p = {p_test}")
                    print(f"✓ Found q = {q_test}")
                    
                    # Decrypt
                    phi_n = (p_test - 1) * (q_test - 1) * (y - 1)
                    
                    try:
                        d = pow(e, -1, phi_n)
                        m = pow(c, d, n)
                        flag = long_to_bytes(m)
                        print(f"\n🎉 FLAG: {flag.decode()}")
                        exit()
                    except Exception as ex:
                        print(f"Decryption error: {ex}")
                        continue

print("Could not solve with this approach either...")

# Let's try one more thing - maybe use an online factorization service
print(f"\nIf manual methods fail, try factoring these numbers online:")
print(f"x*z = {xz}")
print(f"You can try: http://factordb.com/")
