from Cryptodome.Util.number import long_to_bytes
import math
from sympy import factorint

# Given values
hint1 = ***************************************
hint2 = 130654136341446094800376989317102537325
n = 1291778230841963634710522186531131140292748304311790700929719174642140386189828346122801056721461179519840234314280632436994655344881023892312594913853574461748121277453328656446109784054563731
e = 9397905637403387422411461938505089525132522490010480161341814566119369497062528168320590767152928258571447916140517
c = 482782367816881259357312883356702175242817718119063880833819462767226937212873552015335218158868462980872863563953024168114906381978834311555560455076311389674805493391941801398577027462103318

print("=== Using SymPy for Factorization ===")

# Step 1: Get y from gcd
y = math.gcd(n, e)
print(f"y = {y}")

# Step 2: Get p*q and x*z
pq = n // y
xz = e // y
print(f"p*q = {pq}")
print(f"x*z = {xz}")

# Step 3: Use SymPy to factor x*z
print(f"\nFactoring x*z = {xz} using SymPy...")
print("This might take a while for large numbers...")

try:
    factors = factorint(xz)
    print(f"Factors of x*z: {factors}")
    
    if len(factors) == 2:
        factor_list = list(factors.keys())
        x_candidate = factor_list[0]
        z_candidate = factor_list[1]
        
        print(f"Found x = {x_candidate}, z = {z_candidate}")
        
        def extended_gcd(a, b):
            if a == 0:
                return b, 0, 1
            gcd, x1, y1 = extended_gcd(b % a, a)
            x = y1 - (b // a) * x1
            y = x1
            return gcd, x, y

        def crt_two(a1, m1, a2, m2):
            """Chinese Remainder Theorem for two congruences"""
            gcd, u, v = extended_gcd(m1, m2)
            if (a2 - a1) % gcd != 0:
                return None
            lcm = m1 * m2 // gcd
            solution = (a1 + m1 * ((a2 - a1) // gcd) * u) % lcm
            return solution
        
        # Try both assignments of x and z
        for x, z in [(x_candidate, z_candidate), (z_candidate, x_candidate)]:
            print(f"\nTrying x={x}, z={z}")
            
            # Use CRT to find p: p ≡ hint1 (mod x), p ≡ hint2 (mod z)
            p_base = crt_two(hint1, x, hint2, z)
            if p_base is None:
                print("CRT failed for this assignment")
                continue
            
            print(f"CRT solution: p ≡ {p_base} (mod {x*z})")
            
            # Find the correct p by trying p = p_base + k*(x*z)
            mod_xz = x * z
            for k in range(-1000, 1001):
                p_test = p_base + k * mod_xz
                if p_test <= 0:
                    continue
                    
                if pq % p_test == 0:
                    q_test = pq // p_test
                    
                    # Verify the solution
                    if p_test * q_test * y == n:
                        print(f"✓ Found valid p = {p_test}")
                        print(f"✓ Found valid q = {q_test}")
                        print(f"✓ Verification: p*q*y = {p_test * q_test * y}")
                        print(f"✓ Original n = {n}")
                        print(f"✓ Match: {p_test * q_test * y == n}")
                        
                        # Decrypt the message
                        phi_n = (p_test - 1) * (q_test - 1) * (y - 1)
                        print(f"φ(n) = {phi_n}")
                        
                        gcd_e_phi = math.gcd(e, phi_n)
                        print(f"gcd(e, φ(n)) = {gcd_e_phi}")
                        
                        if gcd_e_phi == 1:
                            try:
                                d = pow(e, -1, phi_n)
                                print(f"Private exponent d = {d}")
                                
                                m = pow(c, d, n)
                                print(f"Decrypted message (as number): {m}")
                                
                                flag = long_to_bytes(m)
                                print(f"\n🎉 FLAG: {flag.decode()}")
                                exit()
                            except Exception as ex:
                                print(f"Decryption error: {ex}")
                                continue
                        else:
                            print(f"gcd(e, φ(n)) = {gcd_e_phi} ≠ 1, need special handling")
                            # For cases where gcd(e, φ(n)) > 1, we need more complex decryption
                            continue
        
        print("Could not find valid p and q with the factorization")
    else:
        print(f"Unexpected number of factors: {len(factors)}")
        print(f"Factors: {factors}")

except Exception as ex:
    print(f"SymPy factorization failed: {ex}")
    print("The number might be too large or have special properties")
    
    # Fallback: try a manual approach with smaller search space
    print("\nTrying manual search for factors...")
    
    # Since x and z are 128-bit primes, they're in the range [2^127, 2^128)
    # Let's try to search more systematically
    
    def is_prime(n):
        """Simple primality test"""
        if n < 2:
            return False
        if n == 2:
            return True
        if n % 2 == 0:
            return False
        for i in range(3, int(n**0.5) + 1, 2):
            if n % i == 0:
                return False
        return True
    
    # Try searching around likely values
    sqrt_xz = int(xz ** 0.5)
    print(f"sqrt(x*z) ≈ {sqrt_xz}")
    
    # Search around the square root
    for offset in range(1, 100000):
        for candidate in [sqrt_xz - offset, sqrt_xz + offset]:
            if candidate > 0 and xz % candidate == 0:
                other = xz // candidate
                if candidate != other:  # Make sure they're different
                    print(f"Found factors: {candidate} * {other} = {xz}")
                    print(f"Verification: {candidate * other == xz}")
                    
                    # Continue with the solution using these factors
                    # (This would be the same logic as above)
                    break
        else:
            continue
        break
