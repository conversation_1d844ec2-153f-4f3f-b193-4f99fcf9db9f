# Custom RSA - Crypto CTF Challenge Writeup

**Challenge:** Custom RSA  
**Points:** 418  
**Category:** Crypto  
**Flag:** `Blitz{H0w_D4r3_y0u_br34k_My_RSA_Ag41n!!!}`

## Challenge Description

> Hey, I implemented RSA, though I'm new to cryptography.... I tried tinkering a bit with RSA but now I'm stuck and can't recover the flag. Please help me!

## Files Provided

- `Custom_RSA.py` - The RSA implementation
- `out_4.txt` - Output containing the encrypted data and hints

## Analysis

### Custom RSA Implementation

Looking at `Custom_RSA.py`, we can see a non-standard RSA implementation:

```python
from Cryptodome.Util.number import getPrime, bytes_to_long

m = b"Blitz{REDACTED}"

p = getPrime(256)
q = getPrime(256)
x = getPrime(128)
y = getPrime(128)
z = getPrime(128)
e = x*y*z
n = p*q*y
hint1 = p % x
hint2 = p % z

print("hint1 = ", hint1)
print("hint2 = ", hint2)
print("n = ", n)
print("e = ", e)
c = pow(bytes_to_long(m), e, n)
print("c = ", c)
```

### Key Observations

1. **Non-standard modulus**: `n = p × q × y` (instead of standard `n = p × q`)
2. **Custom exponent**: `e = x × y × z`
3. **Shared factor**: Both `n` and `e` contain the factor `y`
4. **Hints provided**: `hint1 = p % x` and `hint2 = p % z`

### The Vulnerability

The critical vulnerability is that **gcd(n, e) = y > 1**, which violates the fundamental RSA requirement that the modulus and exponent must be coprime.

## Solution

### Step 1: Find the Common Factor

```python
y = math.gcd(n, e)
# y = 215200262830198930084990116270235828097
```

### Step 2: Extract Components

With `y` known, we can extract:
- `p × q = n ÷ y`
- `x × z = e ÷ y`

```python
pq = n // y  # p * q
xz = e // y  # x * z = 43670511893466817304299909907002615691278303256460838105977779390928286517861
```

### Step 3: Factor x × z

The number `x × z` needs to be factored. Since `x` and `z` are 128-bit primes, this is a challenging factorization problem. Using online factorization tools (like Alpertron's ECM calculator), we find:

- `x = 205985756524450894105569840071389752521`
- `z = 212007435030018912792096086712981924541`

### Step 4: Use Chinese Remainder Theorem

With the hints and known factors, we can find `p` using the Chinese Remainder Theorem:
- `p ≡ hint1 (mod x)`
- `p ≡ hint2 (mod z)`

```python
def crt_two(a1, m1, a2, m2):
    """Chinese Remainder Theorem for two congruences"""
    gcd, u, v = extended_gcd(m1, m2)
    if (a2 - a1) % gcd != 0:
        return None
    lcm = m1 * m2 // gcd
    solution = (a1 + m1 * ((a2 - a1) // gcd) * u) % lcm
    return solution

p_base = crt_two(hint1, x, hint2, z)
```

### Step 5: Find the Correct p

Since `p` is a 256-bit prime, we need to find the correct value by trying:
`p = p_base + k × (x × z)` for various values of `k`.

We test each candidate `p` by checking if `(p × q × y) = n`.

### Step 6: Decrypt

Once we have all factors:
- `p = 63017057007516112529458209480818776099872841563354259598215782816720271939801`
- `q = 95254838929140857731905485091211098805477420291191142444776026292696496882123`
- `x = 205985756524450894105569840071389752521`
- `y = 215200262830198930084990116270235828097`
- `z = 212007435030018912792096086712981924541`

We can compute:
```python
phi_n = (p - 1) * (q - 1) * (y - 1)
d = pow(e, -1, phi_n)
m = pow(c, d, n)
flag = long_to_bytes(m)
```

## Final Solution Script

```python
from Cryptodome.Util.number import long_to_bytes
import math

# Given values from out_4.txt
hint1 = ***************************************
hint2 = 130654136341446094800376989317102537325
n = 1291778230841963634710522186531131140292748304311790700929719174642140386189828346122801056721461179519840234314280632436994655344881023892312594913853574461748121277453328656446109784054563731
e = 9397905637403387422411461938505089525132522490010480161341814566119369497062528168320590767152928258571447916140517
c = 482782367816881259357312883356702175242817718119063880833819462767226937212873552015335218158868462980872863563953024168114906381978834311555560455076311389674805493391941801398577027462103318

# Find common factor
y = math.gcd(n, e)
pq = n // y
xz = e // y

# Known factors from factorization
x = 205985756524450894105569840071389752521
z = 212007435030018912792096086712981924541

# Use CRT to find p
def extended_gcd(a, b):
    if a == 0:
        return b, 0, 1
    gcd, x1, y1 = extended_gcd(b % a, a)
    x = y1 - (b // a) * x1
    y = x1
    return gcd, x, y

def crt_two(a1, m1, a2, m2):
    gcd, u, v = extended_gcd(m1, m2)
    if (a2 - a1) % gcd != 0:
        return None
    lcm = m1 * m2 // gcd
    solution = (a1 + m1 * ((a2 - a1) // gcd) * u) % lcm
    return solution

p_base = crt_two(hint1, x, hint2, z)
mod_xz = x * z

# Find correct p
for k in range(-1000, 1001):
    p_test = p_base + k * mod_xz
    if p_test > 0 and pq % p_test == 0:
        q_test = pq // p_test
        if p_test * q_test * y == n:
            # Decrypt
            phi_n = (p_test - 1) * (q_test - 1) * (y - 1)
            d = pow(e, -1, phi_n)
            m = pow(c, d, n)
            flag = long_to_bytes(m)
            print(f"FLAG: {flag.decode()}")
            break
```

## Key Takeaways

1. **Custom RSA implementations** often introduce vulnerabilities
2. **Shared factors** between `n` and `e` break RSA security
3. **Mathematical relationships** and hints can be exploited using number theory
4. **Factorization tools** are essential for breaking large composite numbers
5. **Chinese Remainder Theorem** is powerful for solving systems of congruences

## Flag

`Blitz{H0w_D4r3_y0u_br34k_My_RSA_Ag41n!!!}`
