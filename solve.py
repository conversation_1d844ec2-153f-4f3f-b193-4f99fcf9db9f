from Cryptodome.Util.number import long_to_bytes
import math

# Given values from out_4.txt
hint1 = ***************************************
hint2 = 130654136341446094800376989317102537325
n = 1291778230841963634710522186531131140292748304311790700929719174642140386189828346122801056721461179519840234314280632436994655344881023892312594913853574461748121277453328656446109784054563731
e = 9397905637403387422411461938505089525132522490010480161341814566119369497062528168320590767152928258571447916140517
c = 482782367816881259357312883356702175242817718119063880833819462767226937212873552015335218158868462980872863563953024168114906381978834311555560455076311389674805493391941801398577027462103318

print("=== Custom RSA Challenge Solution ===")
print("From the code:")
print("- n = p * q * y")
print("- e = x * y * z")
print("- hint1 = p % x")
print("- hint2 = p % z")
print("- p, q are 256-bit primes")
print("- x, y, z are 128-bit primes")

# Step 1: Find y as the common factor
print("\n[Step 1] Finding common factor y...")
y = math.gcd(n, e)
print(f"y = gcd(n, e) = {y}")

# Step 2: Extract p*q and x*z
pq = n // y
xz = e // y
print(f"\n[Step 2] Extracted values:")
print(f"p * q = n / y = {pq}")
print(f"x * z = e / y = {xz}")

# Step 3: Use a more efficient approach to find x and z
print(f"\n[Step 3] Finding x and z...")
print(f"x * z = {xz}")
print(f"Bit length of x*z: {xz.bit_length()}")

# Try a different approach: use the mathematical relationship more directly
def solve_custom_rsa():
    """
    We have:
    - n = p * q * y
    - e = x * y * z
    - hint1 = p % x
    - hint2 = p % z

    Strategy: Use the hints to constrain p, then find the factors
    """

    def extended_gcd(a, b):
        if a == 0:
            return b, 0, 1
        gcd, x1, y1 = extended_gcd(b % a, a)
        x = y1 - (b // a) * x1
        y = x1
        return gcd, x, y

    def chinese_remainder_theorem(remainders, moduli):
        """Solve system of congruences using CRT"""
        if len(remainders) != 2 or len(moduli) != 2:
            return None

        a1, m1 = remainders[0], moduli[0]
        a2, m2 = remainders[1], moduli[1]

        gcd, u, v = extended_gcd(m1, m2)
        if (a2 - a1) % gcd != 0:
            return None

        lcm = m1 * m2 // gcd
        solution = (a1 + m1 * ((a2 - a1) // gcd) * u) % lcm
        return solution

    # Try to factor x*z using trial division with optimizations
    print(f"[Step 4] Factoring x*z = {xz}")
    print(f"Since x,z are 128-bit primes, trying efficient factorization...")

    # Try small factors first
    for i in range(2, 10000):
        if xz % i == 0:
            x_candidate = i
            z_candidate = xz // i
            print(f"Found small factor: x={x_candidate}, z={z_candidate}")
            break
    else:
        # Use Pollard's rho for larger factors
        def pollard_rho(n):
            if n % 2 == 0:
                return 2
            x, y, d = 2, 2, 1
            f = lambda x: (x * x + 1) % n

            while d == 1:
                x = f(x)
                y = f(f(y))
                d = math.gcd(abs(x - y), n)

            return d if d != n else None

        print("Trying Pollard's rho...")
        factor = pollard_rho(xz)
        if factor:
            x_candidate = factor
            z_candidate = xz // factor
            print(f"Pollard's rho found: x={x_candidate}, z={z_candidate}")
        else:
            print("Factorization failed")
            return False

    # Try both assignments of x and z
    for x, z in [(x_candidate, z_candidate), (z_candidate, x_candidate)]:
        print(f"\n[Step 5] Trying x={x}, z={z}")

        # Use CRT to find p: p ≡ hint1 (mod x), p ≡ hint2 (mod z)
        p_base = chinese_remainder_theorem([hint1, hint2], [x, z])
        if p_base is None:
            print("CRT failed for this assignment")
            continue

        print(f"CRT solution: p ≡ {p_base} (mod {x*z})")

        # Find the correct p by trying p = p_base + k*(x*z)
        xz_mod = x * z
        for k in range(1000):
            for sign in [1, -1]:
                if k == 0 and sign == -1:
                    continue

                p_candidate = p_base + sign * k * xz_mod
                if p_candidate <= 0:
                    continue

                if pq % p_candidate == 0:
                    q_candidate = pq // p_candidate

                    # Verify the solution
                    if p_candidate * q_candidate * y == n:
                        print(f"✓ Found valid p={p_candidate}")
                        print(f"✓ Found valid q={q_candidate}")

                        # Decrypt the message
                        phi_n = (p_candidate - 1) * (q_candidate - 1) * (y - 1)

                        try:
                            d = pow(e, -1, phi_n)
                            m = pow(c, d, n)
                            flag = long_to_bytes(m)
                            print(f"\n🎉 FLAG: {flag.decode()}")
                            return True
                        except:
                            print("Decryption failed")
                            continue

    return False

# Run the solution
if solve_custom_rsa():
    print("Challenge solved!")
else:
    print("Could not solve the challenge")
